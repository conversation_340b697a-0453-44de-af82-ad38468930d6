import path from "node:path";
import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import tailwindcss from "tailwindcss";
import autoprefixer from "autoprefixer";

export default defineConfig(({ command, mode }) => ({
  plugins: [
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_lazyRouteDiscovery: true,
        v3_relativeSplatPath: true,
        v3_singleFetch: true,
        v3_throwAbortReason: true,
      },
    }),
    tsconfigPaths(),
  ],
  // Suppress Vite CJS deprecation warning
  logLevel: process.env.VITE_CJS_IGNORE_WARNING ? 'error' : (command === 'build' ? 'warn' : 'info'),
  css: {
    postcss: {
      plugins: [
        tailwindcss,
        autoprefixer,
      ],
    },
  },
  resolve: {
    alias: {
      "~": path.resolve(__dirname, "app"),
      "~/": path.resolve(__dirname, "app") + "/",
    },
  },
  server: {
    port: 3001,
  },
  build: {
    target: "esnext",
  },
  ssr: {
    noExternal: ["victory-vendor"],
  },
  optimizeDeps: {
    exclude: ["@remix-run/remix"],
    include: ["victory-vendor", "recharts"],
  },
  // Force ESM mode
  define: {
    global: 'globalThis',
  },
}));
