/* CSS Variables for HVAC CRM Theme System */

:root {
  /* Base Colors */
  --background: 210 20% 98%; /* Light gray background */
  --foreground: 222.2 84% 4.9%; /* Dark text */

  /* Card Colors */
  --card: 0 0% 100%; /* White cards */
  --card-foreground: 222.2 84% 4.9%; /* Dark text on cards */

  /* Popover Colors */
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;

  /* Primary Colors - A more professional blue */
  --primary: 210 80% 40%; /* Muted blue */
  --primary-foreground: 210 40% 98%; /* Light text on primary */
  --primary-rgb: 51, 102, 153; /* RGB for the new primary blue */

  /* Secondary Colors - Soft neutral gray */
  --secondary: 210 20% 90%; /* Lighter gray */
  --secondary-foreground: 222.2 84% 4.9%; /* Dark text on secondary */

  /* Muted Colors */
  --muted: 210 20% 90%;
  --muted-foreground: 215.4 16.3% 46.9%;

  /* Accent Colors - A slightly softer orange */
  --accent: 30 90% 50%;
  --accent-foreground: 60 9.1% 97.8%;

  /* Destructive Colors - Red */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;

  /* Success Colors - Green */
  --success: 142.1 76.2% 36.3%;
  --success-foreground: 355.7 100% 97.3%;

  /* Warning Colors - Amber */
  --warning: 32.2 94.6% 43.7%;
  --warning-foreground: 60 9.1% 97.8%;

  /* Info Colors - Cyan */
  --info: 198.6 88.7% 48.4%;
  --info-foreground: 210 40% 98%;

  /* Border and Input */
  --border: 210 20% 80%; /* Lighter border */
  --input: 210 20% 80%; /* Lighter input border */
  --ring: 210 80% 40%; /* Primary color for ring */

  /* Border Radius */
  --radius: 0.5rem;

  /* HVAC Specific Colors */
  --hvac-cooling: 198.6 88.7% 48.4%; /* Cyan for cooling */
  --hvac-heating: 14.3 100% 53.1%; /* Orange-red for heating */
  --hvac-ventilation: 142.1 76.2% 36.3%; /* Green for ventilation */
  --hvac-maintenance: 32.2 94.6% 43.7%; /* Amber for maintenance */
  --hvac-emergency: 0 84.2% 60.2%; /* Red for emergency */

  /* Status Colors */
  --status-active: 142.1 76.2% 36.3%;
  --status-inactive: 215.4 16.3% 46.9%;
  --status-pending: 32.2 94.6% 43.7%;
  --status-completed: 142.1 76.2% 36.3%;
  --status-cancelled: 0 84.2% 60.2%;

  /* Priority Colors */
  --priority-low: 142.1 76.2% 36.3%;
  --priority-medium: 32.2 94.6% 43.7%;
  --priority-high: 14.3 100% 53.1%;
  --priority-urgent: 0 84.2% 60.2%;

  /* Chart Colors */
  --chart-1: 210 80% 40%;
  --chart-2: 30 90% 50%;
  --chart-3: 142.1 76.2% 36.3%;
  --chart-4: 32.2 94.6% 43.7%;
  --chart-5: 198.6 88.7% 48.4%;
}

.dark {
  /* Base Colors */
  --background: 220 20% 15%; /* Dark gray background */
  --foreground: 210 40% 98%; /* Light text */

  /* Card Colors */
  --card: 220 20% 20%; /* Slightly lighter dark gray for cards */
  --card-foreground: 210 40% 98%;

  /* Popover Colors */
  --popover: 220 20% 20%;
  --popover-foreground: 210 40% 98%;

  /* Primary Colors - Adjusted for dark mode */
  --primary: 210 70% 50%;
  --primary-foreground: 220 20% 15%;
  --primary-rgb: 77, 153, 230;

  /* Secondary Colors - Dark Gray */
  --secondary: 220 20% 25%;
  --secondary-foreground: 210 40% 98%;

  /* Muted Colors */
  --muted: 220 20% 25%;
  --muted-foreground: 215 20.2% 65.1%;

  /* Accent Colors - Adjusted for dark mode */
  --accent: 30 90% 55%;
  --accent-foreground: 220 20% 15%;

  /* Destructive Colors - Red (adjusted for dark) */
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;

  /* Success Colors - Green (adjusted for dark) */
  --success: 142.1 70.6% 45.3%;
  --success-foreground: 220 20% 15%;

  /* Warning Colors - Amber (adjusted for dark) */
  --warning: 32.2 94.6% 43.7%;
  --warning-foreground: 220 20% 15%;

  /* Info Colors - Cyan (adjusted for dark) */
  --info: 198.6 88.7% 48.4%;
  --info-foreground: 220 20% 15%;

  /* Border and Input */
  --border: 220 20% 30%;
  --input: 220 20% 30%;
  --ring: 210 70% 50%;

  /* HVAC Specific Colors (adjusted for dark) */
  --hvac-cooling: 198.6 88.7% 48.4%;
  --hvac-heating: 14.3 100% 53.1%;
  --hvac-ventilation: 142.1 70.6% 45.3%;
  --hvac-maintenance: 32.2 94.6% 43.7%;
  --hvac-emergency: 0 62.8% 30.6%;

  /* Status Colors (adjusted for dark) */
  --status-active: 142.1 70.6% 45.3%;
  --status-inactive: 215 20.2% 65.1%;
  --status-pending: 32.2 94.6% 43.7%;
  --status-completed: 142.1 70.6% 45.3%;
  --status-cancelled: 0 62.8% 30.6%;

  /* Priority Colors (adjusted for dark) */
  --priority-low: 142.1 70.6% 45.3%;
  --priority-medium: 32.2 94.6% 43.7%;
  --priority-high: 14.3 100% 53.1%;
  --priority-urgent: 0 62.8% 30.6%;

  /* Chart Colors (adjusted for dark) */
  --chart-1: 210 70% 50%;
  --chart-2: 30 90% 55%;
  --chart-3: 142.1 70.6% 45.3%;
  --chart-4: 32.2 94.6% 43.7%;
  --chart-5: 198.6 88.7% 48.4%;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced base styles for HVAC CRM */
@layer base {
  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Theme transition class for smooth theme changes */
  html.theme-transition,
  html.theme-transition *,
  html.theme-transition *:before,
  html.theme-transition *:after {
    transition: all 0.3s ease-in-out !important;
    transition-delay: 0 !important;
  }

  body {
    @apply font-sans text-gray-900 bg-gray-50 antialiased;
    background-image:
      radial-gradient(at 100% 0%, rgba(59, 130, 246, 0.1) 0px, transparent 50%),
      radial-gradient(at 0% 100%, rgba(249, 115, 22, 0.1) 0px, transparent 50%);
    background-attachment: fixed;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight;
  }

  h1 {
    @apply text-3xl md:text-4xl leading-tight;
    letter-spacing: -0.02em;
  }

  h2 {
    @apply text-2xl md:text-3xl leading-tight;
    letter-spacing: -0.01em;
  }

  h3 {
    @apply text-xl md:text-2xl leading-snug;
  }

  h4 {
    @apply text-lg md:text-xl font-medium leading-snug;
  }

  p {
    @apply leading-relaxed;
  }

  /* Improved focus styles for accessibility */
  :focus-visible {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2 ring-offset-white transition-shadow duration-200;
  }

  /* Smooth transitions for theme changes */
  * {
    @apply transition-colors duration-200;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400 transition-colors;
  }

  /* Selection styling */
  ::selection {
    @apply bg-blue-100 text-gray-900;
  }
}

/* Enhanced animations and utilities for HVAC CRM */
@layer utilities {
  /* Gradient text variations */
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-green-600;
  }

  .text-gradient-warm {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-orange-600 to-yellow-600;
  }

  .text-gradient-cool {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600;
  }

  /* Enhanced hover effects for cards and interactive elements */
  .hover-lift {
    @apply transition-all duration-300 hover:translate-y-[-3px] hover:shadow-lg;
  }

  .hover-glow {
    @apply transition-all duration-300;
    box-shadow: 0 0 0 rgba(59, 130, 246, 0);
  }

  .hover-glow:hover {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
  }

  .hover-scale {
    @apply transition-all duration-300 hover:scale-[1.02];
  }

  .hover-border {
    @apply transition-all duration-300 border border-transparent;
  }

  .hover-border:hover {
    @apply border-blue-300;
  }

  /* Shimmer effect for loading states or highlights */
  .shimmer {
    @apply relative overflow-hidden;
  }

  .shimmer::after {
    @apply absolute inset-0;
    content: "";
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    animation: shimmer 2s infinite;
    background-size: 200% 100%;
  }

  .dark .shimmer::after {
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0) 100%
    );
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Enhanced glass effects */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20 shadow-lg;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-black/20 shadow-lg;
  }

  .glass-card {
    @apply bg-white/80 dark:bg-black/40 backdrop-blur-md border border-white/30 dark:border-white/10 shadow-lg;
  }

  .glass-primary {
    @apply bg-primary/10 backdrop-blur-md border border-primary/20 shadow-lg;
  }

  /* Status indicators */
  .status-dot {
    @apply h-2.5 w-2.5 rounded-full inline-block;
  }

  .status-dot-pulse {
    @apply status-dot;
    animation: pulse-animation 2s infinite;
  }

  @keyframes pulse-animation {
    0% {
      box-shadow: 0 0 0 0 rgba(var(--color-rgb), 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(var(--color-rgb), 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(var(--color-rgb), 0);
    }
  }

  /* Modern landing page animations */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
    opacity: 0;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }

  .animate-scale {
    animation: scaleIn 0.5s ease-out forwards;
    opacity: 0;
    transform: scale(0.95);
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes fadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    }
    to {
      box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
    }
  }

  /* Enhanced gradient text utilities */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent;
  }

  .text-gradient-success {
    @apply bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent;
  }

  .text-gradient-warning {
    @apply bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent;
  }

  /* Interactive button effects */
  .btn-icon-pulse {
    @apply transition-all duration-300 hover:scale-110;
  }

  .btn-icon-pulse:hover {
    animation: iconPulse 0.6s ease-in-out;
  }

  @keyframes iconPulse {
    0%, 100% {
      transform: scale(1.1);
    }
    50% {
      transform: scale(1.2);
    }
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl;
  }

  .card-hover:hover {
    transform: translateY(-5px) scale(1.02);
  }

  /* Backdrop blur utilities */
  .backdrop-blur-glass {
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
  }
}


