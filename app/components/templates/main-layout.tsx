import type { User } from "@prisma/client";
import { Footer } from "~/components/organisms/footer";
import { Header } from "~/components/organisms/header";
import { Navigation } from "~/components/organisms/navigation";
import { OfflineIndicator, OfflineBanner } from "~/components/ui/offline-indicator";
import type { UserRole, Notification } from "~/types/shared";

interface MainLayoutProps {
  user: User | null;
  children: React.ReactNode;
  userRole?: UserRole;
  notifications?: Notification[];
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
}

export function MainLayout({
  user,
  children,
  userRole = 'USER',
  notifications = [],
  onMarkAsRead,
  onMarkAllAsRead
}: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-background text-foreground relative overflow-hidden">
      {/* Subtle background pattern/gradient for overall page */}
      <div className="absolute inset-0 z-0 opacity-30" style={{
        backgroundImage: `radial-gradient(at 20% 80%, hsl(var(--primary)/0.05) 0px, transparent 50%),
                          radial-gradient(at 80% 20%, hsl(var(--accent)/0.05) 0px, transparent 50%)`
      }} />

      {/* Offline Banner */}
      <OfflineBanner />

      {/* Main Grid Layout */}
      <div className="relative z-10 grid min-h-screen grid-rows-[auto_1fr_auto] lg:grid-cols-[280px_1fr] shadow-xl rounded-lg overflow-hidden m-4 lg:m-8">
        {/* Header - spans full width */}
        <Header
          user={user}
          userRole={userRole}
          notifications={notifications}
          onMarkAsRead={onMarkAsRead}
          onMarkAllAsRead={onMarkAllAsRead}
          className="lg:col-span-2 bg-card/80 backdrop-blur-md border-b border-border/40 shadow-sm"
        />

        {/* Sidebar Navigation - hidden on mobile, visible on desktop */}
        <aside className="hidden lg:block bg-card/70 backdrop-blur-md border-r border-border/40 overflow-y-auto shadow-inner">
          <div className="sticky top-0 p-6">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-foreground/90 mb-4">Nawigacja</h2>
            </div>
            <Navigation userRole={userRole} className="space-y-1" />
          </div>
        </aside>

        {/* Main Content Area */}
        <main className="bg-background/90 backdrop-blur-sm overflow-y-auto">
          <div className="container mx-auto px-4 py-10 lg:px-8 lg:py-12">
            <div className="max-w-7xl mx-auto bg-card/80 backdrop-blur-sm rounded-lg shadow-lg p-6 lg:p-8 border border-border/30">
              {children}
            </div>
          </div>
        </main>

        {/* Footer - spans full width */}
        <Footer className="lg:col-span-2 border-t border-border/40 bg-card/40 backdrop-blur-sm shadow-inner" />
      </div>

      {/* Mobile Navigation - shows on mobile only */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-card/90 backdrop-blur-md border-t border-border/40 z-50 shadow-lg">
        <Navigation userRole={userRole} />
      </div>

      {/* Offline Indicator */}
      <div className="fixed bottom-4 right-4 z-50">
        <OfflineIndicator />
      </div>
    </div>
  );
}