import { Link, isRouteErrorResponse, useRouteError } from "@remix-run/react";
import { useEffect } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { captureException } from "../utils/monitoring.client";

/**
 * Custom error boundary component for handling errors in the application
 */
export function ErrorBoundary() {
  const error = useRouteError();
  
  // Log the error to monitoring service
  useEffect(() => {
    if (error instanceof Error) {
      captureException(error);
    }
  }, [error]);
  
  // Handle different types of errors
  if (isRouteErrorResponse(error)) {
    // This is a response error (404, 401, etc.)
    return (
      <ErrorDisplay
        status={error.status}
        title={`${error.status} - ${error.statusText}`}
        message={error.data?.message || "An unexpected error occurred."}
      />
    );
  }
  
  // Handle standard errors
  if (error instanceof Error) {
    return (
      <ErrorDisplay
        status={500}
        title="Application Error"
        message={error.message || "An unexpected error occurred."}
      />
    );
  }
  
  // Handle unknown errors
  return (
    <ErrorDisplay
      status={500}
      title="Unknown Error"
      message="An unexpected error occurred. Please try again later."
    />
  );
}

interface ErrorDisplayProps {
  status: number;
  title: string;
  message: string;
}

function ErrorDisplay({ status, title, message }: ErrorDisplayProps) {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-muted">
            <span className="text-3xl font-bold text-muted-foreground">{status}</span>
          </div>
          <CardTitle className="text-2xl font-bold">{title}</CardTitle>
          <CardDescription>
            {message}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-md bg-muted p-4">
            <div className="flex items-center space-x-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-muted-foreground"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p className="text-sm text-muted-foreground">
                {status === 404
                  ? "The page you're looking for doesn't exist or has been moved."
                  : "We've logged this error and will work on fixing it as soon as possible."}
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button
            className="w-full"
            onClick={() => {
              window.location.reload();
            }}
          >
            Try Again
          </Button>
          <Button variant="outline" className="w-full" asChild>
            <Link to="/">Return to Home</Link>
          </Button>
        </CardFooter>
      </Card>
      <p className="mt-8 text-center text-sm text-muted-foreground">
        If the problem persists, please contact support.
      </p>
    </div>
  );
}
