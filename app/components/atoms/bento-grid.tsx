/**
 * Enhanced Bento Grid Component - Modern Layout System
 * Inspired by Japanese bento boxes for modular dashboard layouts
 */

import React from "react";
import { cn } from "~/lib/utils";

export interface BentoGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg';
  autoRows?: 'min' | 'max' | 'fr';
  padding?: 'sm' | 'md' | 'lg';
}

export const BentoGrid: React.FC<BentoGridProps> = ({
  children,
  className,
  columns = 4,
  gap = 'md',
  autoRows = 'min',
  padding = 'lg'
}) => {
  return (
    <div className={cn(
      "grid",
      // Responsive grid columns
      {
        'grid-cols-1': columns === 1,
        'grid-cols-1 md:grid-cols-2': columns === 2,
        'grid-cols-1 md:grid-cols-2 lg:grid-cols-3': columns === 3,
        'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4': columns === 4,
        'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5': columns === 5,
        'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6': columns === 6,
      },
      // Gap sizes
      {
        'gap-2': gap === 'sm',
        'gap-4': gap === 'md',
        'gap-6': gap === 'lg',
      },
      // Auto rows
      {
        'auto-rows-min': autoRows === 'min',
        'auto-rows-max': autoRows === 'max',
        'auto-rows-fr': autoRows === 'fr',
      },
      // Padding
      {
        'p-2': padding === 'sm',
        'p-4 md:p-6': padding === 'md',
        'p-6 md:p-8': padding === 'lg',
      },
      className
    )}>
      {children}
    </div>
  );
};
