import type { User } from "@prisma/client";
import { <PERSON> } from "@remix-run/react";
import { ModeToggle } from "~/components/mode-toggle";
import { NotificationCenter } from "~/components/organisms/notification-center";
import { Button } from "~/components/ui/button";
import type { UserRole, Notification } from "~/types/shared";

interface HeaderProps {
  user: User | null;
  userRole: UserRole;
  notifications?: Notification[];
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
  className?: string;
}

export function Header({
  user,
  userRole,
  notifications = [],
  onMarkAsRead,
  onMarkAllAsRead,
  className
}: HeaderProps) {
  return (
    <header className={`sticky top-0 z-40 w-full border-b border-border/40 bg-card/80 backdrop-blur-md supports-[backdrop-filter]:bg-card/60 shadow-sm ${className}`}>
      <div className="container mx-auto px-4 lg:px-6 py-3">
        <div className="flex h-16 items-center justify-between">
          {/* <PERSON><PERSON> and Brand */}
          <div className="flex items-center gap-3">
            <Link to="/" className="flex items-center gap-3 group">
              <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-primary to-primary-foreground flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <span className="text-white font-bold text-xl">H</span>
              </div>
              <div className="hidden sm:block">
                <span className="text-2xl font-heading font-extrabold text-gradient bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                  HVAC CRM
                </span>
                <div className="text-xs text-muted-foreground font-medium tracking-wide">
                  Servicetool Pro
                </div>
              </div>
            </Link>
          </div>

          {/* User menu and controls */}
          <div className="flex items-center gap-4">
            {/* Notifications center */}
            {user && (
              <NotificationCenter
                notifications={notifications}
                onMarkAsRead={onMarkAsRead}
                onMarkAllAsRead={onMarkAllAsRead}
              />
            )}

            {/* Theme toggle */}
            <ModeToggle />

            {/* User menu */}
            {user ? (
              <div className="relative group">
                <button
                  type="button"
                  className="flex items-center gap-2 text-sm font-medium transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-full"
                  aria-expanded="false"
                >
                  <span className="sr-only">Open user menu</span>
                  <div className="h-11 w-11 rounded-full bg-gradient-to-br from-primary/30 to-accent/30 ring-2 ring-primary/50 flex items-center justify-center text-primary-foreground font-semibold text-lg shadow-md hover:shadow-lg transition-all duration-200">
                    {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                  </div>
                  <div className="hidden lg:block text-left">
                    <div className="font-medium text-foreground">{user.name || user.email}</div>
                    <div className="text-xs text-muted-foreground">{userRole}</div>
                  </div>
                </button>
                <div className="hidden group-hover:block absolute right-0 z-10 mt-3 w-60 origin-top-right rounded-xl bg-card/95 backdrop-blur-xl py-2 shadow-2xl ring-1 ring-border/50 focus:outline-none transition-all duration-300 animate-in fade-in-50 slide-in-from-top-5">
                  <div className="px-4 py-3 text-sm border-b border-border/50">
                    <div className="font-medium text-foreground">{user.name || user.email}</div>
                    <div className="text-xs text-muted-foreground">{userRole}</div>
                  </div>
                  <Link
                    to="/settings"
                    className="flex items-center gap-2 px-4 py-2 text-sm hover:bg-accent/10 hover:text-accent transition-colors rounded-lg mx-2 my-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-settings"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.08a2 2 0 0 1 1 1.74v.18a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                    Ustawienia
                  </Link>
                  <Link
                    to="/logout"
                    className="flex items-center gap-2 px-4 py-2 text-sm hover:bg-destructive/10 hover:text-destructive transition-colors rounded-lg mx-2 my-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-log-out"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/><polyline points="17 16 22 12 17 8"/><line x1="22" x2="10" y1="12" y2="12"/></svg>
                    Wyloguj
                  </Link>
                </div>
              </div>
            ) : (
              <Button asChild variant="default" size="sm" className="shadow-md hover:shadow-lg transition-all duration-200">
                <Link to="/login">Zaloguj</Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}