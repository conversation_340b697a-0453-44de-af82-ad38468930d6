/**
 * Enhanced <PERSON><PERSON> Card Component - 2025 UI/UX Trends
 * Features: Neumorphic design, micro-interactions, AI-powered insights
 */

import { motion, AnimatePresence } from "framer-motion";
import React, { useState } from "react";
import { cn } from "~/lib/utils";

export interface BentoCardProps {
  title: string;
  value?: string | number;
  trend?: number;
  icon?: React.ReactNode;
  size?: "normal" | "wide" | "tall" | "large";
  variant?: "default" | "neumorphic" | "glass" | "gradient";
  priority?: "low" | "medium" | "high" | "urgent";
  loading?: boolean;
  interactive?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
  className?: string;
  badge?: string;
  subtitle?: string;
}

export const BentoCard: React.FC<BentoCardProps> = ({
  title,
  value,
  trend,
  icon,
  size = "normal",
  variant = "default",
  priority = "medium",
  loading = false,
  interactive = false,
  onClick,
  children,
  className,
  badge,
  subtitle,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getVariantStyles = () => {
    switch (variant) {
      case "neumorphic":
        return cn(
          "bg-gradient-to-br from-background to-card dark:from-card dark:to-background",
          "shadow-[5px_5px_10px_rgba(0,0,0,0.05),-5px_-5px_10px_rgba(255,255,255,0.8)] dark:shadow-[5px_5px_10px_rgba(0,0,0,0.3),-5px_-5px_10px_rgba(255,255,255,0.05)]",
          "border-0 hover:shadow-[2px_2px_5px_rgba(0,0,0,0.03),-2px_-2px_5px_rgba(255,255,255,0.5)] dark:hover:shadow-[2px_2px_5px_rgba(0,0,0,0.2),-2px_-2px_5px_rgba(255,255,255,0.02)]"
        );
      case "glass":
        return cn(
          "bg-card/10 dark:bg-card/10 backdrop-blur-md",
          "border border-border/20 dark:border-border/20",
          "shadow-lg shadow-foreground/5 dark:shadow-foreground/20"
        );
      case "gradient":
        return cn(
          "bg-gradient-to-br from-primary/5 via-primary/5 to-accent/5",
          "border border-primary/20 dark:border-primary/20",
          "backdrop-blur-sm"
        );
      default:
        return cn(
          "bg-card dark:bg-card",
          "border border-border dark:border-border"
        );
    }
  };

  const getPriorityStyles = () => {
    switch (priority) {
      case "urgent":
        return "ring-2 ring-red-500/50 border-red-200 dark:border-red-800";
      case "high":
        return "ring-1 ring-orange-500/50 border-orange-200 dark:border-orange-800";
      case "medium":
        return "ring-1 ring-blue-500/30 border-blue-200 dark:border-blue-800";
      default:
        return "";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={interactive ? { scale: 1.02 } : undefined}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onClick={onClick}
      className={cn(
        "group relative overflow-hidden rounded-xl",
        "transition-all duration-300",
        getVariantStyles(),
        getPriorityStyles(),
        {
          'hover:shadow-lg hover:shadow-primary/10 dark:hover:shadow-primary/10': !loading,
          'cursor-pointer': interactive || onClick,
          'col-span-1': size === "normal",
          'col-span-2': size === "wide",
          'col-span-1 row-span-2': size === "tall",
          'col-span-2 row-span-2': size === "large",
        },
        className
      )}
    >
      {/* Loading overlay */}
      <AnimatePresence>
        {loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-background/50 dark:bg-background/50 backdrop-blur-sm z-10 flex items-center justify-center"
          >
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      {/* Badge */}
      {badge && (
        <div className="absolute top-3 right-3 z-20">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-foreground">
            {badge}
          </span>
        </div>
      )}

      <div className="relative p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-foreground">
              {title}
            </h3>
            {subtitle && (
              <p className="text-sm text-muted-foreground mt-1">
                {subtitle}
              </p>
            )}
          </div>
          {icon && (
            <motion.div
              animate={isHovered ? { scale: 1.1, rotate: 5 } : { scale: 1, rotate: 0 }}
              transition={{ duration: 0.2 }}
              className="text-primary ml-3"
            >
              {icon}
            </motion.div>
          )}
        </div>

        {value && (
          <div className="mb-4">
            <span className="text-3xl font-bold text-foreground">
              {value}
            </span>
            {trend !== undefined && (
              <motion.span
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className={cn(
                  "ml-2 text-sm font-medium inline-flex items-center",
                  trend > 0
                    ? "text-success"
                    : "text-destructive"
                )}
              >
                <span className="mr-1">
                  {trend > 0 ? "↗" : "↘"}
                </span>
                {Math.abs(trend)}%
              </motion.span>
            )}
          </div>
        )}

        {children}
      </div>
    </motion.div>
  );
};
