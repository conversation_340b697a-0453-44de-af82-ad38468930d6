/**
 * Event Bus Service
 *
 * This service provides a centralized event bus for the application using Redis Streams.
 * It allows components to publish events and subscribe to events without direct coupling.
 */

import { createClient } from 'redis';
import { v4 as uuidv4 } from 'uuid';
import { prisma } from '~/db.server';

// Define event types
export enum EventType {
  // Service Order events
  SERVICE_ORDER_CREATED = 'service_order.created',
  SERVICE_ORDER_UPDATED = 'service_order.updated',
  SERVICE_ORDER_DELETED = 'service_order.deleted',
  SERVICE_ORDER_COMPLETED = 'service_order.completed',

  // Customer events
  CUSTOMER_CREATED = 'customer.created',
  CUSTOMER_UPDATED = 'customer.updated',
  CUSTOMER_DELETED = 'customer.deleted',

  // Device events
  DEVICE_CREATED = 'device.created',
  DEVICE_UPDATED = 'device.updated',
  DEVICE_DELETED = 'device.deleted',

  // Calendar events
  CALENDAR_ENTRY_CREATED = 'calendar.created',
  CALE<PERSON>AR_ENTRY_UPDATED = 'calendar.updated',
  CALENDAR_ENTRY_DELETED = 'calendar.deleted',
  CALENDAR_SYNCED = 'calendar.synced',

  // User events
  USER_CREATED = 'user.created',
  USER_UPDATED = 'user.updated',
  USER_DELETED = 'user.deleted',
  USER_LOGGED_IN = 'user.logged_in',
  USER_LOGGED_OUT = 'user.logged_out',

  // Notification events
  NOTIFICATION_CREATED = 'notification.created',
  NOTIFICATION_READ = 'notification.read',

  // System events
  SYSTEM_ERROR = 'system.error',
  SYSTEM_WARNING = 'system.warning',
  SYSTEM_INFO = 'system.info',
}

// Define event interface
export interface Event {
  id: string;
  type: EventType;
  data: any;
  metadata: {
    timestamp: number;
    userId?: string;
    correlationId?: string;
    source?: string;
  };
}

// Redis client configuration
const REDIS_URL = process.env.REDIS_URL || 'redis://redis:6379';
const STREAM_KEY = 'events_stream';
const CONSUMER_GROUP = 'hvac_crm_consumers';
// Disable Redis by default in development if not explicitly enabled
const REDIS_ENABLED = process.env.REDIS_ENABLED === 'true' || process.env.NODE_ENV === 'production';

// Create Redis client
let redisClient: ReturnType<typeof createClient> | null = null;

/**
 * Initialize the Redis client
 */
async function getRedisClient() {
  // If Redis is disabled, return a mock client
  if (!REDIS_ENABLED) {
    console.log('Redis is disabled. Using mock client for event bus.');
    return createMockRedisClient();
  }

  if (!redisClient) {
    try {
      redisClient = createClient({
        url: REDIS_URL,
        socket: {
          reconnectStrategy: (retries) => {
            // Limit retries in development
            if (retries > 3 && process.env.NODE_ENV === 'development') {
              console.log('Redis connection failed after 3 retries. Switching to mock client.');
              return false; // Stop retrying
            }
            // Exponential backoff with max delay of 10 seconds
            const delay = Math.min(Math.pow(2, retries) * 100, 10000);
            console.log(`Redis reconnecting in ${delay}ms...`);
            return delay;
          },
          connectTimeout: 3000, // 3 second timeout for faster fallback
        }
      });

      redisClient.on('error', (err) => {
        console.warn('Redis client error (falling back to mock):', err.message);
      });

      await redisClient.connect();
      console.log('Redis client connected successfully');

      // Create consumer group if it doesn't exist
      try {
        await redisClient.xGroupCreate(STREAM_KEY, CONSUMER_GROUP, '0', {
          MKSTREAM: true,
        });
      } catch (err: any) {
        // Ignore BUSYGROUP error (group already exists)
        if (err.message.indexOf('BUSYGROUP') === -1) {
          console.error('Error creating consumer group:', err);
        }
      }
    } catch (error) {
      console.warn('Failed to initialize Redis client, using mock client:', error instanceof Error ? error.message : error);
      redisClient = null; // Reset to allow retry
      return createMockRedisClient();
    }
  }

  return redisClient;
}

/**
 * Publish an event to the event bus
 *
 * @param type - The type of event
 * @param data - The event data
 * @param metadata - Additional metadata for the event
 * @returns The ID of the published event
 */
export async function publishEvent(
  type: EventType,
  data: any,
  metadata: Partial<Event['metadata']> = {}
): Promise<string> {
  const client = await getRedisClient();
  const event: Event = {
    id: uuidv4(),
    type,
    data,
    metadata: {
      timestamp: Date.now(),
      ...metadata,
    },
  };

  try {
    // Add event to Redis stream
    await client.xAdd(STREAM_KEY, '*', {
      event: JSON.stringify(event),
    });

    console.log(`Event published: ${type}`, { eventId: event.id });
  } catch (redisError: any) {
    // Log the error but don't throw it - allow the application to continue
    console.warn(`Failed to publish event to Redis: ${type}`, {
      eventId: event.id,
      error: redisError.message
    });

    // Also store the event in the database for durability
    try {
      await prisma.event.create({
        data: {
          type: event.type,
          data: JSON.stringify(event.data),
          metadata: JSON.stringify(event.metadata),
        }
      });
    } catch (dbError) {
      console.error('Failed to store event in database:', dbError);
    }
  }

  return event.id;
}

/**
 * Subscribe to events of specific types
 *
 * @param eventTypes - Array of event types to subscribe to
 * @param handler - Function to handle events
 * @param consumerId - Unique ID for this consumer
 */
export async function subscribeToEvents(
  eventTypes: EventType[],
  handler: (event: Event) => Promise<void>,
  consumerId: string = uuidv4()
): Promise<() => Promise<void>> {
  const client = await getRedisClient();

  // Flag to control the consumer loop
  let isRunning = true;

  // Start consuming events in a separate process
  (async () => {
    while (isRunning) {
      try {
        // Read new messages from the stream
        const messages = await client.xReadGroup(
          CONSUMER_GROUP,
          consumerId,
          { [STREAM_KEY]: '>' },
          { COUNT: 10, BLOCK: 2000 }
        );

        if (!messages || messages.length === 0) {
          continue;
        }

        for (const message of messages[0].messages) {
          try {
            const eventJson = message.message.event as string;
            if (!eventJson) {
              console.warn('Received message with no event data:', message);
              await client.xAck(STREAM_KEY, CONSUMER_GROUP, message.id);
              continue;
            }

            const event: Event = JSON.parse(eventJson);

            // Check if this consumer is interested in this event type
            if (eventTypes.includes(event.type)) {
              try {
                await handler(event);

                // Acknowledge the message
                await client.xAck(STREAM_KEY, CONSUMER_GROUP, message.id);
              } catch (error) {
                console.error(`Error handling event ${event.id}:`, error);
                // Don't acknowledge the message so it can be reprocessed
              }
            } else {
              // Acknowledge events we're not interested in
              await client.xAck(STREAM_KEY, CONSUMER_GROUP, message.id);
            }
          } catch (parseError) {
            console.error('Error parsing event message:', parseError);
            // Acknowledge malformed messages to avoid infinite retries
            await client.xAck(STREAM_KEY, CONSUMER_GROUP, message.id);
          }
        }
      } catch (error) {
        console.error('Error consuming events:', error);
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  })();

  // Return a function to unsubscribe
  return async () => {
    isRunning = false;
  };
}

/**
 * Get all events from the stream (for debugging/admin purposes)
 *
 * @param count - Maximum number of events to retrieve
 * @returns Array of events
 */
export async function getAllEvents(count: number = 100): Promise<Event[]> {
  const client = await getRedisClient();

  try {
    const messages = await client.xRevRange(STREAM_KEY, '+', '-', {
      COUNT: count,
    });

    return messages.map(message => {
      try {
        const eventJson = message.message.event as string;
        if (!eventJson) return null;
        return JSON.parse(eventJson);
      } catch (parseError) {
        console.warn('Error parsing event:', parseError);
        return null;
      }
    }).filter(Boolean) as Event[];
  } catch (redisError) {
    console.warn('Error getting events from Redis:', redisError);

    // Fall back to database if Redis fails
    try {
      const dbEvents = await prisma.event.findMany({
        take: count,
        orderBy: { createdAt: 'desc' }
      });

      return dbEvents.map(dbEvent => {
        try {
          return {
            id: dbEvent.id,
            type: dbEvent.type as EventType,
            data: JSON.parse(dbEvent.data),
            metadata: JSON.parse(dbEvent.metadata)
          };
        } catch (parseError) {
          console.warn('Error parsing DB event:', parseError);
          return null;
        }
      }).filter(Boolean) as Event[];
    } catch (dbError) {
      console.error('Error getting events from database:', dbError);
      return [];
    }
  }
}

/**
 * Get events of a specific type
 *
 * @param eventType - The type of events to retrieve
 * @param count - Maximum number of events to retrieve
 * @returns Array of events
 */
export async function getEventsByType(
  eventType: EventType,
  count: number = 100
): Promise<Event[]> {
  try {
    const allEvents = await getAllEvents(count * 2); // Get more events to account for filtering
    return allEvents.filter(event => event.type === eventType).slice(0, count);
  } catch (error) {
    console.error('Error in getEventsByType:', error);
    return [];
  }
}

/**
 * Clean up resources when shutting down
 */
export async function closeEventBus(): Promise<void> {
  try {
    if (redisClient) {
      await redisClient.quit();
      redisClient = null;
    }
  } catch (error) {
    console.error('Error closing event bus:', error);
  }
}
