import type { MetaFunction } from "@remix-run/node";
import { Link } from "@remix-run/react";
import {
  <PERSON>ch,
  Zap,
  Shield,
  TrendingUp,
  Users,
  Calendar,
  Search,
  BarChart3,
  Sparkles,
  ArrowRight,
  Star,
  Gauge,
  MapPin,
  Phone,
  Calculator
} from "lucide-react";
import { useState, useEffect } from "react";

import { ModeToggle } from "~/components/mode-toggle";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { ServiceAreaMap } from "~/components/fulmark/ServiceAreaMap";
import { ProductCatalog } from "~/components/fulmark/ProductCatalog";
import { Testimonials } from "~/components/website/Testimonials";
import { FAQ } from "~/components/website/FAQ";
import { ContactForm } from "~/components/website/ContactForm";
import { StructuredData } from "~/components/seo/StructuredData";
import { useOptionalUser } from "~/utils";
import { fulmarkData, contactInfo } from "~/data/fulmark-data";

export const meta: MetaFunction = () => [
  { title: "Fulmark HVAC CRM - Profesjonalne Zarządzanie Serwisem Klimatyzacji" },
  { name: "description", content: "Nowoczesny system CRM dla firmy Fulmark. Zarządzanie klientami, urządzeniami klimatyzacyjnymi i zleceniami serwisowymi z wykorzystaniem AI." }
];

export default function Index() {
  const user = useOptionalUser();
  const [isVisible, setIsVisible] = useState(false);
  const [currentFeature, setCurrentFeature] = useState(0);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % 6);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const features = [
    { icon: Wrench, text: "Montaż Klimatyzacji", color: "text-blue-400" },
    { icon: Users, text: "Serwis i Naprawa", color: "text-green-400" },
    { icon: Calendar, text: "Planowanie Zleceń", color: "text-purple-400" },
    { icon: Shield, text: "Gwarancja i Jakość", color: "text-orange-400" },
    { icon: MapPin, text: "Warszawa i Okolice", color: "text-red-400" },
    { icon: Star, text: "20 Lat Doświadczenia", color: "text-yellow-400" }
  ];

  return (
    <>
      {/* SEO Structured Data */}
      <StructuredData type="organization" />
      <StructuredData type="localBusiness" />
      <StructuredData type="service" />

      <main className="min-h-screen bg-background text-foreground overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-accent/10 to-primary/10 animate-pulse"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Header */}
      <div className="relative z-10 container mx-auto px-6 py-12">
        <div className="flex justify-between items-center mb-20">
          <div className={`transition-all duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
            <h1 className="text-6xl font-black bg-gradient-to-r from-primary via-accent to-primary-foreground bg-clip-text text-transparent">
              {fulmarkData.company.name}
            </h1>
            <p className="text-xl text-muted-foreground mt-3 font-light">{fulmarkData.company.tagline}</p>
            <div className="flex items-center gap-6 mt-4">
              <div className="flex items-center gap-2 text-base text-muted-foreground">
                <Star className="h-5 w-5 text-warning" />
                <span>{fulmarkData.company.experience}</span>
              </div>
              <div className="flex items-center gap-2 text-base text-muted-foreground">
                <Phone className="h-5 w-5 text-success" />
                <span>{contactInfo.phone}</span>
              </div>
            </div>
          </div>
          <div className={`transition-all duration-1000 delay-300 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'}`}>
            <ModeToggle />
          </div>
        </div>

        {/* Hero Section */}
        <div className="text-center mb-24">
          <div className={`transition-all duration-1000 delay-500 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <h2 className="text-7xl font-extrabold mb-8 bg-gradient-to-r from-foreground via-primary/20 to-accent/20 bg-clip-text text-transparent leading-tight">
              Profesjonalna Klimatyzacja
            </h2>
            <p className="text-xl text-muted-foreground mb-10 max-w-4xl mx-auto leading-relaxed">
              {fulmarkData.company.description}. Nowoczesny system CRM wspiera
              zarządzanie klientami, urządzeniami i zleceniami serwisowymi.
            </p>
            <div className="flex justify-center items-center gap-8 mb-12">
              <div className="flex items-center gap-3 px-6 py-3 bg-card/50 rounded-full border border-border/70 shadow-md">
                <MapPin className="h-5 w-5 text-primary" />
                <span className="text-foreground">Warszawa i okolice</span>
              </div>
              <div className="flex items-center gap-3 px-6 py-3 bg-card/50 rounded-full border border-border/70 shadow-md">
                <Shield className="h-5 w-5 text-success" />
                <span className="text-foreground">LG & Daikin</span>
              </div>
            </div>

            {/* Animated Feature Showcase */}
            <div className="flex justify-center items-center gap-6 mb-16">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div
                    key={index}
                    className={`transition-all duration-500 ${
                      currentFeature === index
                        ? 'scale-125 opacity-100'
                        : 'scale-100 opacity-50'
                    }`}
                  >
                    <div className={`p-5 rounded-full bg-card/50 backdrop-blur-sm border border-border/70 shadow-lg ${feature.color}`}>
                      <Icon className="h-9 w-9" />
                    </div>
                  </div>
                );
              })}
            </div>

            {features[currentFeature] && (
              <p className={`text-xl font-semibold transition-all duration-500 ${features[currentFeature].color}`}>
                {features[currentFeature].text}
              </p>
            )}
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
          {/* Welcome Card */}
          <Card className={`bg-slate-800/50 backdrop-blur-sm border-slate-700 text-white transition-all duration-1000 delay-700 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/20 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
            <CardHeader className="relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full -translate-y-16 translate-x-16"></div>
              <CardTitle className="text-2xl font-bold text-white flex items-center gap-3">
                <Sparkles className="h-6 w-6 text-yellow-400" />
                Witaj w HVAC CRM
              </CardTitle>
              <CardDescription className="text-slate-300 text-lg">
                Profesjonalne narzędzie dla nowoczesnych firm serwisowych
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-slate-300 leading-relaxed">
                Zarządzaj klientami, urządzeniami i zleceniami serwisowymi z wykorzystaniem
                najnowszych technologii AI i automatyzacji procesów biznesowych.
              </p>

              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <Input
                    placeholder="Szukaj klientów, urządzeń, zleceń..."
                    className="pl-10 bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 focus:border-purple-400 focus:ring-purple-400/20"
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col gap-4">
              {user ? (
                <div className="flex gap-4 w-full">
                  <Button asChild className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 transition-all duration-300 hover:scale-105">
                    <Link to="/dashboard" className="flex items-center justify-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      Dashboard
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button asChild variant="outline" className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white transition-all duration-300">
                    <Link to="/search" className="flex items-center justify-center gap-2">
                      <Search className="h-4 w-4" />
                      Szukaj
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Website Actions */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                    <Button asChild className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold transition-all duration-300 hover:scale-105">
                      <Link to="/kontakt" className="flex items-center justify-center gap-2">
                        <Phone className="h-4 w-4" />
                        Kontakt
                      </Link>
                    </Button>
                    <Button asChild className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold transition-all duration-300 hover:scale-105">
                      <Link to="/rezerwacja" className="flex items-center justify-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Umów wizytę
                      </Link>
                    </Button>
                    <Button asChild className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold transition-all duration-300 hover:scale-105">
                      <Link to="/kalkulator" className="flex items-center justify-center gap-2">
                        <Calculator className="h-4 w-4" />
                        Kalkulator
                      </Link>
                    </Button>
                  </div>

                  {/* CRM Access */}
                  <div className="flex gap-4 w-full">
                    <Button asChild variant="outline" className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white transition-all duration-300">
                      <Link to="/join">Rejestracja CRM</Link>
                    </Button>
                    <Button asChild variant="outline" className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white transition-all duration-300">
                      <Link to="/login">Logowanie CRM</Link>
                    </Button>
                  </div>
                </div>
              )}
            </CardFooter>
          </Card>

          {/* Features Card */}
          <Card className={`bg-slate-800/50 backdrop-blur-sm border-slate-700 text-white transition-all duration-1000 delay-900 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/20 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'}`}>
            <CardHeader className="relative overflow-hidden">
              <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-green-500/20 to-blue-500/20 rounded-full -translate-y-16 -translate-x-16"></div>
              <CardTitle className="text-2xl font-bold text-white flex items-center gap-3">
                <Gauge className="h-6 w-6 text-green-400" />
                Kluczowe Funkcje
              </CardTitle>
              <CardDescription className="text-slate-300 text-lg">
                Wszystko czego potrzebujesz w jednym miejscu
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { icon: Users, text: "Zarządzanie klientami z detalowymi profilami", color: "text-blue-400" },
                  { icon: Wrench, text: "Śledzenie urządzeń z historią serwisową", color: "text-green-400" },
                  { icon: Calendar, text: "Zarządzanie zleceniami z trackingiem statusu", color: "text-purple-400" },
                  { icon: BarChart3, text: "Integracja z kalendarzem Microsoft Outlook", color: "text-orange-400" },
                  { icon: Search, text: "Wyszukiwanie semantyczne z AI Bielik", color: "text-yellow-400" },
                  { icon: TrendingUp, text: "Kompleksowy dashboard z kluczowymi metrykami", color: "text-pink-400" },
                  { icon: Shield, text: "Tryb ciemny dla komfortowego przeglądania", color: "text-red-400" }
                ].map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div
                      key={index}
                      className={`flex items-start space-x-3 p-3 rounded-lg bg-slate-700/30 hover:bg-slate-700/50 transition-all duration-300 hover:scale-105 animate-fade-in`}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className={`p-2 rounded-full bg-slate-800 ${feature.color}`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <span className="text-slate-300 leading-relaxed">{feature.text}</span>
                    </div>
                  );
                })}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:text-white hover:border-transparent transition-all duration-300 hover:scale-105">
                <Star className="h-4 w-4 mr-2" />
                Dowiedz się więcej
              </Button>
            </CardFooter>
          </Card>
        </div>

        {/* Stats Section */}
        <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 mb-20 transition-all duration-1000 delay-1100 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          {[
            { label: "Lat Doświadczenia", value: fulmarkData.stats.experience, icon: Star, color: "text-yellow-400" },
            { label: "Zrealizowanych Projektów", value: fulmarkData.stats.completedProjects, icon: Wrench, color: "text-green-400" },
            { label: "Zadowolonych Klientów", value: fulmarkData.stats.satisfiedCustomers, icon: Users, color: "text-blue-400" },
            { label: "Obszar Działania", value: "Warszawa+", icon: MapPin, color: "text-purple-400" }
          ].map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="bg-slate-800/30 backdrop-blur-sm border-slate-700 text-center hover:scale-110 transition-all duration-300 hover:shadow-lg">
                <CardContent className="pt-6">
                  <div className={`inline-flex p-3 rounded-full bg-slate-700/50 mb-4 ${stat.color}`}>
                    <Icon className="h-6 w-6" />
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                  <div className="text-sm text-slate-400">{stat.label}</div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Service Area Section */}
        <div className={`mb-20 transition-all duration-1000 delay-1200 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <ServiceAreaMap />
        </div>

        {/* Product Catalog Section */}
        <div className={`mb-20 transition-all duration-1000 delay-1300 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <ProductCatalog />
        </div>

        {/* Testimonials Section */}
        <div className={`mb-20 transition-all duration-1000 delay-1400 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <Testimonials variant="carousel" maxItems={6} />
        </div>

        {/* FAQ Section */}
        <div className={`mb-20 transition-all duration-1000 delay-1500 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <FAQ variant="accordion" showCategories={true} maxItems={8} />
        </div>

        {/* Contact Form Section */}
        <div className={`mb-20 transition-all duration-1000 delay-1600 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <ContactForm source="homepage" />
        </div>

        {/* Footer */}
        <div className={`text-center transition-all duration-1000 delay-1400 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <div className="space-y-4">
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-slate-800/50 backdrop-blur-sm rounded-full border border-slate-700">
              <Sparkles className="h-5 w-5 text-yellow-400" />
              <span className="text-slate-300">{fulmarkData.company.fullName}</span>
              <span className="text-slate-500">•</span>
              <span className="text-slate-400">{fulmarkData.company.experience}</span>
            </div>
            <div className="flex justify-center items-center gap-6 text-sm text-slate-400">
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span>{contactInfo.phone}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                <span>Warszawa, Piaseczno</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span>LG & Daikin</span>
              </div>
            </div>
            <p className="text-xs text-slate-500">
              Powered by GoBackend-Kratos & Remix
            </p>
          </div>
        </div>
      </div>
      </main>
    </>
  );
}
