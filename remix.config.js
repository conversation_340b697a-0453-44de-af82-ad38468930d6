/** @type {import('@remix-run/dev').AppConfig} */
export default {
  cacheDirectory: "./node_modules/.cache/remix",
  ignoredRouteFiles: ["**/.*", "**/*.test.{ts,tsx}", "**/routes/**/*.server.{ts,tsx}"],
  serverModuleFormat: "esm",
  serverPlatform: "node",
  tailwind: true,
  postcss: true,
  watchPaths: ["./tailwind.config.ts"],

  // Alias configuration for server-side resolution
  serverDependenciesToBundle: [
    /^~/,
    /^@udecode\/.*/,
    /^plate.*/,
    "chart.js",
    "react-chartjs-2",
    "react-day-picker",
    /^marked.*/,
    /^lodash.*/,
    /^date-fns.*/,
    /^gsap.*/,
  ],

  // Enable all future features for better performance and developer experience
  future: {
    v3_fetcherPersist: true,
    v3_lazyRouteDiscovery: true,
    v3_relativeSplatPath: true,
    v3_singleFetch: true,
    v3_throwAbortReason: true,
    v3_meta: true,
    v3_normalizeFormMethod: true,
    v3_errorBoundary: true,
  },



  // Optimize asset loading
  assetsBuildDirectory: "public/build",
  publicPath: "/build/",

  // Enable source maps in production for better error tracking
  sourcemap: true
};